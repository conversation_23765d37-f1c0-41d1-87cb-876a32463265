{"name": "briky-lend", "version": "0.1.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "generate-i18n": "node scripts/generate-i18n-keys.js", "update-version": "node scripts/update-version.js"}, "dependencies": {"@ethersproject/bignumber": "^5.8.0", "@ethersproject/units": "^5.8.0", "@expo/react-native-action-sheet": "^4.1.1", "@hookform/resolvers": "^3.10.0", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "11.4.1", "@react-navigation/bottom-tabs": "^7.3.12", "@react-navigation/native": "^7.0.17", "@react-navigation/native-stack": "^7.3.1", "@react-navigation/stack": "^7.2.1", "@reown/appkit-wagmi-react-native": "^1.2.4", "@tanstack/react-query": "^5.75.5", "@tanstack/react-query-devtools": "^5.75.5", "@walletconnect/react-native-compat": "^2.20.2", "axios": "^1.8.4", "big.js": "^7.0.1", "currency.js": "^2.0.4", "ethers": "^5.8.0", "expo": "^53.0.10", "expo-application": "~6.1.4", "expo-clipboard": "~7.1.4", "expo-image-picker": "~16.1.4", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "i18next": "^24.2.3", "react": "19.0.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-is": "^18.2.0", "react-native": "0.79.3", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.13.1", "react-native-root-toast": "^3.6.0", "react-native-safe-area-context": "5.4.0", "react-native-svg": "15.11.2", "viem": "^2.27.0", "wagmi": "^2.14.16", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/eslint-config": "^3.2.0", "@types/big.js": "^6.2.2", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "8.29.1", "@typescript-eslint/parser": "8.28.0", "@typescript-eslint/scope-manager": "8.29.1", "@typescript-eslint/type-utils": "8.29.1", "@typescript-eslint/types": "8.28.0", "@typescript-eslint/typescript-estree": "8.29.1", "@typescript-eslint/utils": "8.29.1", "@typescript-eslint/visitor-keys": "8.28.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.50.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-native": "^5.0.0", "eslint-visitor-keys": "^3.4.3", "glob": "^10.3.10", "minimatch": "9.0.5", "prettier": "^3.5.3", "ts-api-utils": "2.1.0", "typescript": "~5.8.3"}, "private": true, "engines": {"node": ">=16.0.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["@reown/appkit-wagmi-react-native", "i18next"]}}}, "resolutions": {"@typescript-eslint/eslint-plugin": "8.29.1", "@typescript-eslint/parser": "8.28.0", "@typescript-eslint/scope-manager": "8.29.1", "@typescript-eslint/type-utils": "8.29.1", "@typescript-eslint/types": "8.28.0", "@typescript-eslint/typescript-estree": "8.29.1", "@typescript-eslint/utils": "8.29.1", "@typescript-eslint/visitor-keys": "8.28.0", "eslint-visitor-keys": "^3.4.3", "minimatch": "^3.1.2", "ts-api-utils": "2.1.0", "react-native-modal": "13.0.2"}}