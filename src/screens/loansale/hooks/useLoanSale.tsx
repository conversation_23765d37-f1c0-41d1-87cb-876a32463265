import QueryKeys from "@/config/queryKeys"
import { getListLoanSaleApi } from "@/service"
import { OfferState } from "@/service/types"
import { useQuery } from "@tanstack/react-query"
import useLoadingStore from "@/stores/loadingStore"
import { useEffect, useCallback } from "react"
import { useLoanData } from "@/service/hooks/useLoanData"

export const useLoanSale = () => {
  const { setLoading } = useLoadingStore()
  const { getListOffer } = useLoanData()

  const result = useQuery({
    queryKey: [QueryKeys.EXPLORER.LOAN_SALE],
    queryFn: () =>
      getListOffer(() =>
        getListLoanSaleApi({
          itemsPerPage: 100,
          states: OfferState.SELLING,
        }),
      ),
  })

  const { data, isLoading, error, refetch } = result

  const handleRefresh = useCallback(async () => {
    setLoading(true)
    await refetch()
    setLoading(false)
  }, [refetch, setLoading])

  useEffect(() => {
    setLoading(isLoading)
    return () => setLoading(false)
  }, [isLoading, setLoading])

  const contextValue = {
    offers: data?.list || [],
    totalOffers: data?.pagination.totalItems || 0,
    isLoading,
    error,
    handleRefresh,
  }

  return { contextValue }
}
