import QueryKeys from "@/config/queryKeys"
import { getListLoansApi } from "@/service"
import { MortgageLoanState } from "@/service/types"
import { useQuery } from "@tanstack/react-query"
import useLoadingStore from "@/stores/loadingStore"
import { useEffect, useCallback } from "react"
import { useLoanData } from "@/service/hooks/useLoanData"

export const useAllLoans = () => {
  const { setLoading } = useLoadingStore()
  const { getListLoan } = useLoanData()

  const result = useQuery({
    queryKey: [QueryKeys.EXPLORER.ALL_LOANS],
    queryFn: () =>
      getListLoan(() =>
        getListLoansApi({
          itemsPerPage: 100,
          states: MortgageLoanState.PENDING,
        }),
      ),
  })

  const { data, isLoading, error, refetch } = result

  const handleRefresh = useCallback(async () => {
    setLoading(true)
    await refetch()
    setLoading(false)
  }, [refetch, setLoading])

  useEffect(() => {
    setLoading(isLoading)
    return () => setLoading(false)
  }, [isLoading, setLoading])

  const contextValue = {
    loans: data?.list || [],
    totalLoans: data?.pagination.totalItems || 0,
    isLoading,
    error,
    handleRefresh,
  }

  return { contextValue }
}
